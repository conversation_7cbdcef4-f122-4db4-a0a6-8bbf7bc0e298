'use client'

import { <PERSON>actNode, useEffect, useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { LoadingOverlay } from '@/components/ui/LoadingOverlay'

interface AppWrapperProps {
  children: ReactNode
}

export function AppWrapper({ children }: AppWrapperProps) {
  const { isLoading, isCreatingUser, isUserReady, user } = useAuth()
  const [showLoadingOverlay, setShowLoadingOverlay] = useState(false)
  const [hasShownInitialLoad, setHasShownInitialLoad] = useState(false)

  // Determine if we should show the loading overlay
  const shouldShowOverlay = isLoading || isCreatingUser || (!isUserReady && !hasShownInitialLoad)

  useEffect(() => {
    // Show overlay immediately if we're in a loading state
    if (shouldShowOverlay) {
      setShowLoadingOverlay(true)
    } else {
      // Once user is ready, mark that we've shown initial load
      if (isUserReady && user) {
        setHasShownInitialLoad(true)
      }
      
      // Small delay before hiding overlay to prevent flashing
      const timer = setTimeout(() => {
        setShowLoadingOverlay(false)
      }, 200)
      
      return () => clearTimeout(timer)
    }
  }, [shouldShowOverlay, isUserReady, user])

  // Reset the initial load flag when user changes (e.g., after cache clear)
  useEffect(() => {
    if (!user) {
      setHasShownInitialLoad(false)
    }
  }, [user])

  const getLoadingMessage = () => {
    if (isLoading) {
      return {
        message: "Initializing application...",
        subMessage: "Setting up your session"
      }
    }
    
    if (isCreatingUser) {
      return {
        message: "Creating your session...",
        subMessage: "This may take a moment after clearing cache"
      }
    }
    
    return {
      message: "Setting up your session...",
      subMessage: "Please wait while we prepare everything"
    }
  }

  const { message, subMessage } = getLoadingMessage()

  return (
    <>
      {/* Main app content with conditional blur */}
      <div className={showLoadingOverlay ? 'app-blur' : 'app-blur-disabled'}>
        {children}
      </div>
      
      {/* Loading overlay */}
      <LoadingOverlay 
        isVisible={showLoadingOverlay}
        message={message}
        subMessage={subMessage}
      />
    </>
  )
}
