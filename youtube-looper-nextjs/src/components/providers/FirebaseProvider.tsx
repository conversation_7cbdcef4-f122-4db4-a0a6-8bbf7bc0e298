'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { FirebaseApp } from 'firebase/app'
import { Firestore } from 'firebase/firestore'
import { Auth } from 'firebase/auth'
import { initializeFirebase } from '@/lib/firebase/config'

interface FirebaseContextType {
  app: FirebaseApp | null
  db: Firestore | null
  auth: Auth | null
  isInitialized: boolean
  error: string | null
}

const FirebaseContext = createContext<FirebaseContextType>({
  app: null,
  db: null,
  auth: null,
  isInitialized: false,
  error: null,
})

interface FirebaseProviderProps {
  children: ReactNode
}

export function FirebaseProvider({ children }: FirebaseProviderProps) {
  const [firebaseState, setFirebaseState] = useState<FirebaseContextType>({
    app: null,
    db: null,
    auth: null,
    isInitialized: false,
    error: null,
  })

  useEffect(() => {
    const initFirebase = () => {
      const startTime = performance.now()
      const timestamp = new Date().toISOString()

      try {
        console.log('🔥 Initializing Firebase...', timestamp)
        const firebase = initializeFirebase()
        const endTime = performance.now()

        if (firebase) {
          setFirebaseState({
            app: firebase.app,
            db: firebase.db,
            auth: firebase.auth,
            isInitialized: true,
            error: null,
          })
          console.log('✅ Firebase initialized successfully', {
            duration: `${(endTime - startTime).toFixed(2)}ms`,
            timestamp: new Date().toISOString()
          })
        } else {
          setFirebaseState(prev => ({
            ...prev,
            isInitialized: true,
            error: 'Firebase configuration is incomplete',
          }))
          console.warn('⚠️ Firebase initialization failed - configuration incomplete', {
            duration: `${(endTime - startTime).toFixed(2)}ms`,
            timestamp: new Date().toISOString()
          })
        }
      } catch (error) {
        const endTime = performance.now()
        const errorMessage = error instanceof Error ? error.message : 'Unknown Firebase initialization error'
        setFirebaseState(prev => ({
          ...prev,
          isInitialized: true,
          error: errorMessage,
        }))
        console.error('❌ Firebase initialization error:', {
          error,
          duration: `${(endTime - startTime).toFixed(2)}ms`,
          timestamp: new Date().toISOString()
        })
      }
    }

    // Initialize immediately without async wrapper
    initFirebase()
  }, [])

  return (
    <FirebaseContext.Provider value={firebaseState}>
      {children}
    </FirebaseContext.Provider>
  )
}

export const useFirebase = () => {
  const context = useContext(FirebaseContext)
  if (!context) {
    throw new Error('useFirebase must be used within a FirebaseProvider')
  }
  return context
}
