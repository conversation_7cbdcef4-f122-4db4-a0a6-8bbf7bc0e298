'use client'

import { ReactNode, useState, useEffect } from 'react'
import {
  signInWithPopup,
  linkWithPopup,
  GoogleAuthProvider,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInAnonymously as firebaseSignInAnonymously,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile as firebaseUpdateProfile,
  onAuthStateChanged,
  linkWithCredential,
  EmailAuthProvider,
  User as FirebaseUser
} from 'firebase/auth'
import { AuthContext } from '@/hooks/useAuth'
import { User } from '@/lib/types/auth'
import { useFirebase } from './FirebaseProvider'
import { FirebaseService } from '@/lib/services/firebase'

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreatingUser, setIsCreatingUser] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { auth, db, isInitialized } = useFirebase()

  const isAuthenticated = !!user
  const isUserReady = isAuthenticated && !isCreatingUser

  // Convert Firebase user to our User type
  const convertFirebaseUser = (firebaseUser: FirebaseUser): User => ({
    uid: firebaseUser.uid,
    email: firebaseUser.email,
    displayName: firebaseUser.displayName,
    photoURL: firebaseUser.photoURL,
    emailVerified: firebaseUser.emailVerified,
    isAnonymous: firebaseUser.isAnonymous,
    createdAt: Date.now(),
    lastLoginAt: Date.now(),
  })



  const signInWithGoogle = async (): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      const provider = new GoogleAuthProvider()
      provider.addScope('email')
      provider.addScope('profile')

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        // Use linkWithPopup to link the anonymous account directly
        const result = await linkWithPopup(currentUser, provider)
        const linkedUser = result.user

        console.log('✅ Account linked successfully:', linkedUser.uid)

        // Migrate anonymous user data to the linked account
        if (db && anonymousUid !== linkedUser.uid) {
          console.log('📦 Migrating anonymous user data...')
          const firebaseService = new FirebaseService()
          await firebaseService.migrateAnonymousUserData(anonymousUid, linkedUser.uid)
          console.log('✅ Data migration completed')
        }

        const convertedUser = convertFirebaseUser(linkedUser)
        setUser(convertedUser)
        return convertedUser
      } else {
        // Regular Google sign-in for non-anonymous users
        const result = await signInWithPopup(auth, provider)
        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Google sign in error:', error)
      setError(error.message || 'Failed to sign in with Google')
      return null
    }
  }

  const signInWithEmail = async (email: string, password: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        // Create email credential and link
        const credential = EmailAuthProvider.credential(email, password)
        const result = await linkWithCredential(currentUser, credential)
        const linkedUser = result.user

        console.log('✅ Account linked successfully:', linkedUser.uid)

        // Migrate anonymous user data to the linked account
        if (db && anonymousUid !== linkedUser.uid) {
          console.log('📦 Migrating anonymous user data...')
          const firebaseService = new FirebaseService()
          await firebaseService.migrateAnonymousUserData(anonymousUid, linkedUser.uid)
          console.log('✅ Data migration completed')
        }

        const convertedUser = convertFirebaseUser(linkedUser)
        setUser(convertedUser)
        return convertedUser
      } else {
        // Regular email sign-in for non-anonymous users
        const result = await signInWithEmailAndPassword(auth, email, password)
        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Email sign in error:', error)
      setError(error.message || 'Failed to sign in with email')
      return null
    }
  }

  const signUpWithEmail = async (email: string, password: string, displayName: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        // Create email credential and link
        const credential = EmailAuthProvider.credential(email, password)
        const result = await linkWithCredential(currentUser, credential)
        const linkedUser = result.user

        console.log('✅ Account linked successfully:', linkedUser.uid)

        // Update profile with display name after linking
        if (displayName) {
          await firebaseUpdateProfile(linkedUser, { displayName })
        }

        // Migrate anonymous user data to the linked account
        if (db && anonymousUid !== linkedUser.uid) {
          console.log('📦 Migrating anonymous user data...')
          const firebaseService = new FirebaseService()
          await firebaseService.migrateAnonymousUserData(anonymousUid, linkedUser.uid)
          console.log('✅ Data migration completed')
        }

        const convertedUser = convertFirebaseUser(linkedUser)
        setUser(convertedUser)
        return convertedUser
      } else {
        // Regular email sign-up for non-anonymous users
        const result = await createUserWithEmailAndPassword(auth, email, password)

        // Update profile with display name
        if (displayName) {
          await firebaseUpdateProfile(result.user, { displayName })
        }

        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Email sign up error:', error)
      setError(error.message || 'Failed to create account')
      return null
    }
  }

  const signInAnonymously = async (): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      setIsCreatingUser(true)
      console.log('🔐 Starting anonymous sign in...')

      const result = await firebaseSignInAnonymously(auth)
      const convertedUser = convertFirebaseUser(result.user)

      console.log('✅ Anonymous user created:', convertedUser.uid)
      setUser(convertedUser)
      setIsCreatingUser(false)
      return convertedUser
    } catch (error: any) {
      console.error('Anonymous sign in error:', error)
      setError(error.message || 'Failed to sign in anonymously')
      setIsCreatingUser(false)
      return null
    }
  }

  const signOut = async (): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await firebaseSignOut(auth)
      setUser(null)
    } catch (error: any) {
      console.error('Sign out error:', error)
      setError(error.message || 'Failed to sign out')
    }
  }

  const resetPassword = async (email: string): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await sendPasswordResetEmail(auth, email)
    } catch (error: any) {
      console.error('Reset password error:', error)
      setError(error.message || 'Failed to send password reset email')
    }
  }

  const updateProfile = async (updates: { displayName?: string; photoURL?: string }): Promise<void> => {
    if (!auth?.currentUser) {
      setError('No authenticated user')
      return
    }

    try {
      setError(null)
      await firebaseUpdateProfile(auth.currentUser, updates)

      // Update local user state
      if (user) {
        setUser({
          ...user,
          displayName: updates.displayName || user.displayName,
          photoURL: updates.photoURL || user.photoURL,
        })
      }
    } catch (error: any) {
      console.error('Update profile error:', error)
      setError(error.message || 'Failed to update profile')
    }
  }

  useEffect(() => {
    if (!auth || !isInitialized) {
      setIsLoading(false)
      return
    }

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        console.log('🔄 Auth state changed - user found:', firebaseUser.uid)
        const convertedUser = convertFirebaseUser(firebaseUser)
        setUser(convertedUser)
        setIsCreatingUser(false)
        setIsLoading(false)
      } else {
        console.log('🔄 Auth state changed - no user found')
        // Only try anonymous sign-in if Firebase is properly configured
        const hasValidConfig = process.env.NEXT_PUBLIC_FIREBASE_API_KEY &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID &&
                              process.env.NEXT_PUBLIC_FIREBASE_API_KEY.trim() !== '' &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID.trim() !== ''

        if (hasValidConfig) {
          try {
            console.log('🔐 Auto-signing in anonymously...')
            await signInAnonymously()
          } catch (error) {
            console.error('Failed to sign in anonymously:', error)

            // Retry once after a short delay for network issues
            setTimeout(async () => {
              try {
                console.log('🔄 Retrying anonymous sign in...')
                await signInAnonymously()
              } catch (retryError) {
                console.error('Retry failed:', retryError)
                setIsCreatingUser(false)
                setIsLoading(false)
              }
            }, 1000)
          }
        } else {
          console.log('⚠️ Firebase not configured, skipping anonymous auth')
          setIsCreatingUser(false)
          setIsLoading(false)
        }
      }
    })

    return () => unsubscribe()
  }, [auth, isInitialized])

  const value = {
    user,
    isAuthenticated,
    isLoading,
    isCreatingUser,
    isUserReady,
    error,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signInAnonymously,
    signOut,
    resetPassword,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
