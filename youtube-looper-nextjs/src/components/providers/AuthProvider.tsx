'use client'

import { ReactNode, useState, useEffect } from 'react'
import {
  signInWithPopup,
  linkWithPopup,
  GoogleAuthProvider,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInAnonymously as firebaseSignInAnonymously,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile as firebaseUpdateProfile,
  onAuthStateChanged,
  linkWithCredential,
  EmailAuthProvider,
  User as FirebaseUser
} from 'firebase/auth'
import { AuthContext } from '@/hooks/useAuth'
import { User } from '@/lib/types/auth'
import { useFirebase } from './FirebaseProvider'
import { FirebaseService } from '@/lib/services/firebase'

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreatingUser, setIsCreatingUser] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { auth, db, isInitialized } = useFirebase()

  const isAuthenticated = !!user
  const isUserReady = isAuthenticated && !isCreatingUser

  console.log('🔍 AuthProvider State:', {
    isLoading,
    isCreatingUser,
    isAuthenticated,
    isUserReady,
    userType: user?.isAnonymous ? 'anonymous' : user ? 'authenticated' : 'none',
    userId: user?.uid?.slice(-8) || 'none'
  })

  // Convert Firebase user to our User type
  const convertFirebaseUser = (firebaseUser: FirebaseUser): User => ({
    uid: firebaseUser.uid,
    email: firebaseUser.email,
    displayName: firebaseUser.displayName,
    photoURL: firebaseUser.photoURL,
    emailVerified: firebaseUser.emailVerified,
    isAnonymous: firebaseUser.isAnonymous,
    createdAt: Date.now(),
    lastLoginAt: Date.now(),
  })



  const signInWithGoogle = async (): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      const provider = new GoogleAuthProvider()
      provider.addScope('email')
      provider.addScope('profile')

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        // Use linkWithPopup to link the anonymous account directly
        const result = await linkWithPopup(currentUser, provider)
        const linkedUser = result.user

        console.log('✅ Account linked successfully:', linkedUser.uid)

        // Migrate anonymous user data to the linked account
        if (db && anonymousUid !== linkedUser.uid) {
          console.log('📦 Migrating anonymous user data...')
          const firebaseService = new FirebaseService()
          await firebaseService.migrateAnonymousUserData(anonymousUid, linkedUser.uid)
          console.log('✅ Data migration completed')
        }

        const convertedUser = convertFirebaseUser(linkedUser)
        setUser(convertedUser)
        return convertedUser
      } else {
        // Regular Google sign-in for non-anonymous users
        const result = await signInWithPopup(auth, provider)
        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Google sign in error:', error)
      setError(error.message || 'Failed to sign in with Google')
      return null
    }
  }

  const signInWithEmail = async (email: string, password: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        // Create email credential and link
        const credential = EmailAuthProvider.credential(email, password)
        const result = await linkWithCredential(currentUser, credential)
        const linkedUser = result.user

        console.log('✅ Account linked successfully:', linkedUser.uid)

        // Migrate anonymous user data to the linked account
        if (db && anonymousUid !== linkedUser.uid) {
          console.log('📦 Migrating anonymous user data...')
          const firebaseService = new FirebaseService()
          await firebaseService.migrateAnonymousUserData(anonymousUid, linkedUser.uid)
          console.log('✅ Data migration completed')
        }

        const convertedUser = convertFirebaseUser(linkedUser)
        setUser(convertedUser)
        return convertedUser
      } else {
        // Regular email sign-in for non-anonymous users
        const result = await signInWithEmailAndPassword(auth, email, password)
        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Email sign in error:', error)
      setError(error.message || 'Failed to sign in with email')
      return null
    }
  }

  const signUpWithEmail = async (email: string, password: string, displayName: string): Promise<User | null> => {
    if (!auth) {
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)

      // Check if current user is anonymous
      const currentUser = auth.currentUser
      if (currentUser && currentUser.isAnonymous) {
        console.log('🔗 Current user is anonymous, attempting account linking...')

        // Store the anonymous user ID for data migration
        const anonymousUid = currentUser.uid

        // Create email credential and link
        const credential = EmailAuthProvider.credential(email, password)
        const result = await linkWithCredential(currentUser, credential)
        const linkedUser = result.user

        console.log('✅ Account linked successfully:', linkedUser.uid)

        // Update profile with display name after linking
        if (displayName) {
          await firebaseUpdateProfile(linkedUser, { displayName })
        }

        // Migrate anonymous user data to the linked account
        if (db && anonymousUid !== linkedUser.uid) {
          console.log('📦 Migrating anonymous user data...')
          const firebaseService = new FirebaseService()
          await firebaseService.migrateAnonymousUserData(anonymousUid, linkedUser.uid)
          console.log('✅ Data migration completed')
        }

        const convertedUser = convertFirebaseUser(linkedUser)
        setUser(convertedUser)
        return convertedUser
      } else {
        // Regular email sign-up for non-anonymous users
        const result = await createUserWithEmailAndPassword(auth, email, password)

        // Update profile with display name
        if (displayName) {
          await firebaseUpdateProfile(result.user, { displayName })
        }

        const convertedUser = convertFirebaseUser(result.user)
        setUser(convertedUser)
        return convertedUser
      }
    } catch (error: any) {
      console.error('Email sign up error:', error)
      setError(error.message || 'Failed to create account')
      return null
    }
  }

  const signInAnonymously = async (): Promise<User | null> => {
    if (!auth) {
      console.error('❌ signInAnonymously: Firebase not initialized')
      setError('Firebase not initialized')
      return null
    }

    try {
      setError(null)
      setIsCreatingUser(true)
      console.log('🔐 Starting anonymous sign in...', new Date().toISOString())

      const startTime = performance.now()
      const result = await firebaseSignInAnonymously(auth)
      const endTime = performance.now()

      const convertedUser = convertFirebaseUser(result.user)

      console.log('✅ Anonymous user created:', {
        uid: convertedUser.uid,
        isAnonymous: convertedUser.isAnonymous,
        duration: `${(endTime - startTime).toFixed(2)}ms`,
        timestamp: new Date().toISOString()
      })

      setUser(convertedUser)
      setIsCreatingUser(false)
      return convertedUser
    } catch (error: any) {
      console.error('❌ Anonymous sign in error:', error, new Date().toISOString())
      setError(error.message || 'Failed to sign in anonymously')
      setIsCreatingUser(false)
      return null
    }
  }

  const signOut = async (): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await firebaseSignOut(auth)
      setUser(null)
    } catch (error: any) {
      console.error('Sign out error:', error)
      setError(error.message || 'Failed to sign out')
    }
  }

  const resetPassword = async (email: string): Promise<void> => {
    if (!auth) {
      setError('Firebase not initialized')
      return
    }

    try {
      setError(null)
      await sendPasswordResetEmail(auth, email)
    } catch (error: any) {
      console.error('Reset password error:', error)
      setError(error.message || 'Failed to send password reset email')
    }
  }

  const updateProfile = async (updates: { displayName?: string; photoURL?: string }): Promise<void> => {
    if (!auth?.currentUser) {
      setError('No authenticated user')
      return
    }

    try {
      setError(null)
      await firebaseUpdateProfile(auth.currentUser, updates)

      // Update local user state
      if (user) {
        setUser({
          ...user,
          displayName: updates.displayName || user.displayName,
          photoURL: updates.photoURL || user.photoURL,
        })
      }
    } catch (error: any) {
      console.error('Update profile error:', error)
      setError(error.message || 'Failed to update profile')
    }
  }

  // Add timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoading) {
        console.warn('⚠️ Auth initialization timeout - proceeding without user')
        setIsLoading(false)
        setIsCreatingUser(false)
      }
    }, 3000) // Reduced to 3 second timeout

    return () => clearTimeout(timeout)
  }, [isLoading])

  // Early timeout for Firebase initialization
  useEffect(() => {
    const initTimeout = setTimeout(() => {
      if (!isInitialized) {
        console.warn('⚠️ Firebase initialization taking too long - proceeding without auth')
        setIsLoading(false)
        setIsCreatingUser(false)
      }
    }, 2000) // 2 second timeout for Firebase init

    return () => clearTimeout(initTimeout)
  }, [])

  useEffect(() => {
    if (!isInitialized) {
      return
    }

    // If Firebase is not configured, stop loading immediately
    if (!auth) {
      console.log('⚠️ Firebase not configured - proceeding without authentication')
      setIsLoading(false)
      setIsCreatingUser(false)
      return
    }

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      const timestamp = new Date().toISOString()

      if (firebaseUser) {
        console.log('🔄 Auth state changed - user found:', {
          uid: firebaseUser.uid,
          isAnonymous: firebaseUser.isAnonymous,
          email: firebaseUser.email,
          timestamp
        })

        const convertedUser = convertFirebaseUser(firebaseUser)
        console.log('📝 Setting user state:', {
          uid: convertedUser.uid,
          isAnonymous: convertedUser.isAnonymous,
          timestamp
        })

        setUser(convertedUser)
        setIsCreatingUser(false)
        setIsLoading(false)

        console.log('✅ Auth state update complete:', {
          isLoading: false,
          isCreatingUser: false,
          userType: convertedUser.isAnonymous ? 'anonymous' : 'authenticated',
          timestamp
        })
      } else {
        console.log('🔄 Auth state changed - no user found', timestamp)

        // Only try anonymous sign-in if Firebase is properly configured
        const hasValidConfig = process.env.NEXT_PUBLIC_FIREBASE_API_KEY &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID &&
                              process.env.NEXT_PUBLIC_FIREBASE_API_KEY.trim() !== '' &&
                              process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID.trim() !== ''

        if (hasValidConfig) {
          try {
            console.log('🔐 Auto-signing in anonymously...', timestamp)
            await signInAnonymously()
          } catch (error) {
            console.error('❌ Failed to sign in anonymously:', error, timestamp)

            // Quick retry with shorter delay
            setTimeout(async () => {
              try {
                console.log('🔄 Retrying anonymous sign in...', new Date().toISOString())
                await signInAnonymously()
              } catch (retryError) {
                console.error('❌ Retry failed:', retryError, new Date().toISOString())
                setIsCreatingUser(false)
                setIsLoading(false)
              }
            }, 300)
          }
        } else {
          console.log('⚠️ Firebase not configured, skipping anonymous auth', timestamp)
          setIsCreatingUser(false)
          setIsLoading(false)
        }
      }
    })

    return () => unsubscribe()
  }, [auth, isInitialized])

  const value = {
    user,
    isAuthenticated,
    isLoading,
    isCreatingUser,
    isUserReady,
    error,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signInAnonymously,
    signOut,
    resetPassword,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
