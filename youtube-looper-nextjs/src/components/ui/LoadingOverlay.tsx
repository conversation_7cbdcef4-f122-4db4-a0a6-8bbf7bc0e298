'use client'

import { useEffect, useState } from 'react'

interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
  subMessage?: string
}

export function LoadingOverlay({ 
  isVisible, 
  message = "Setting up your session...", 
  subMessage = "This may take a moment after clearing cache"
}: LoadingOverlayProps) {
  const [showOverlay, setShowOverlay] = useState(false)
  const [animationClass, setAnimationClass] = useState('')

  useEffect(() => {
    if (isVisible) {
      setShowOverlay(true)
      // Small delay to ensure smooth animation
      setTimeout(() => setAnimationClass('opacity-100'), 50)
    } else {
      setAnimationClass('opacity-0')
      // Wait for fade out animation before hiding
      setTimeout(() => setShowOverlay(false), 300)
    }
  }, [isVisible])

  if (!showOverlay) return null

  return (
    <div 
      className={`fixed inset-0 z-[9999] transition-opacity duration-300 ${animationClass}`}
      style={{ 
        background: 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(8px)',
        WebkitBackdropFilter: 'blur(8px)'
      }}
    >
      {/* Blur overlay for the entire screen */}
      <div className="absolute inset-0 bg-black/20" />
      
      {/* Loading content */}
      <div className="relative flex items-center justify-center min-h-screen p-4">
        <div className="glassmorphism-strong p-8 max-w-md w-full text-center">
          {/* Animated logo/spinner */}
          <div className="mb-6 flex justify-center">
            <div className="relative">
              {/* Outer ring */}
              <div className="w-16 h-16 border-4 border-white/20 rounded-full animate-spin">
                <div className="absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-primary-500 rounded-full animate-spin" 
                     style={{ animationDuration: '1.5s' }}></div>
              </div>
              
              {/* Inner ring */}
              <div className="absolute top-2 left-2 w-12 h-12 border-3 border-white/10 rounded-full animate-spin" 
                   style={{ animationDirection: 'reverse', animationDuration: '2s' }}>
                <div className="absolute top-0 left-0 w-full h-full border-3 border-transparent border-t-primary-400 rounded-full animate-spin"
                     style={{ animationDirection: 'reverse', animationDuration: '2s' }}></div>
              </div>
              
              {/* Center icon */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                  <svg 
                    width="14" 
                    height="14" 
                    viewBox="0 0 24 24" 
                    fill="currentColor"
                    className="text-white"
                  >
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Loading text */}
          <div className="space-y-3">
            <h3 className="text-xl font-semibold text-white">
              {message}
            </h3>
            <p className="text-dark-300 text-sm leading-relaxed">
              {subMessage}
            </p>
            
            {/* Animated dots */}
            <div className="flex justify-center space-x-1 mt-4">
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" 
                   style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" 
                   style={{ animationDelay: '200ms' }}></div>
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" 
                   style={{ animationDelay: '400ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
